import 'package:flutter/material.dart';
import 'package:text_generation_video/app/repository/modals/audio_img_to_video/audio_item.dart';
import 'package:text_generation_video/app/widgets/audio/dashed_border.dart';
import 'package:text_generation_video/config/icon_address.dart';

/// 删除音频项时的回调功能
typedef OnDeleteAudioItems = void Function(List<AudioItem> selectedItems);

/// 点击音频项时的回调功能
typedef OnAudioItemTap = void Function(AudioItem audioItem);

/// 点击添加音频按钮时的回调功能
typedef OnAddAudioTap = void Function();

/// 一个具备管理模式和选择功能的可复用AudioList控件组件
class AudioListWidget extends StatefulWidget {
  const AudioListWidget({
    super.key,
    required this.audioItems,
    this.title = "添加配音",
    this.showAddButton = true,
    this.onDeleteAudioItems,
    this.onAudioItemTap,
    this.onAddAudioTap,
    this.crossAxisCount = 3,
    this.mainAxisSpacing = 8.0,
    this.crossAxisSpacing = 8.0,
    this.childAspectRatio = 1.0,
  });

  final List<AudioItem> audioItems;
  final String title;
  final bool showAddButton;
  final OnDeleteAudioItems? onDeleteAudioItems;
  final OnAudioItemTap? onAudioItemTap;
  final OnAddAudioTap? onAddAudioTap;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double childAspectRatio;

  @override
  State<AudioListWidget> createState() => _AudioListWidgetState();
}

class _AudioListWidgetState extends State<AudioListWidget>
    with TickerProviderStateMixin {
  bool _isManagementMode = false;
  final Set<String> _selectedItemIds = <String>{};
  String? _currentSelectedItemId; // 当前选中的音频项ID（非管理模式）
  late AnimationController _managementModeController;
  late Animation<double> _managementModeAnimation;

  @override
  void initState() {
    super.initState();
    _managementModeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _managementModeAnimation = CurvedAnimation(
      parent: _managementModeController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _managementModeController.dispose();
    super.dispose();
  }

  void _toggleManagementMode() {
    setState(() {
      _isManagementMode = !_isManagementMode;
      if (!_isManagementMode) {
        _selectedItemIds.clear();
      }
    });

    if (_isManagementMode) {
      _managementModeController.forward();
    } else {
      _managementModeController.reverse();
    }
  }

  void _selectAudioItem(AudioItem audioItem) {
    setState(() {
      // 如果点击的是当前选中的项，则取消选中
      if (_currentSelectedItemId == audioItem.id) {
        _currentSelectedItemId = null;
      } else {
        _currentSelectedItemId = audioItem.id;
      }
    });

    // 调用回调函数
    widget.onAudioItemTap?.call(audioItem);
  }

  void _toggleItemSelection(String itemId) {
    setState(() {
      if (_selectedItemIds.contains(itemId)) {
        _selectedItemIds.remove(itemId);
      } else {
        _selectedItemIds.add(itemId);
      }
    });
  }

  void _showDeleteConfirmationDialog() {
    if (_selectedItemIds.isEmpty) return;

    final selectedItems = widget.audioItems
        .where((item) => _selectedItemIds.contains(item.id))
        .toList();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _DeleteConfirmationDialog(
        selectedItems: selectedItems,
        onConfirm: () {
          widget.onDeleteAudioItems?.call(selectedItems);
          setState(() {
            _selectedItemIds.clear();
            _isManagementMode = false;
          });
          _managementModeController.reverse();
          Navigator.of(context).pop();
        },
        onCancel: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 12),
          _buildAudioGrid(),
          if (_isManagementMode && _selectedItemIds.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildDeleteButton(),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.title,
          style: const TextStyle(color: Colors.white, fontSize: 16),
        ),
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: _toggleManagementMode,
          child: AnimatedBuilder(
            animation: _managementModeAnimation,
            builder: (context, child) {
              return Row(
                children: [
                  Image.asset(
                    audioManageIcon,
                    width: 16,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    _isManagementMode ? "完成" : "管理",
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAudioGrid() {
    final itemCount = widget.audioItems.length + (widget.showAddButton ? 1 : 0);

    return GridView.builder(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        mainAxisSpacing: widget.mainAxisSpacing,
        crossAxisSpacing: widget.crossAxisSpacing,
        childAspectRatio: widget.childAspectRatio,
      ),
      itemCount: itemCount,
      itemBuilder: (context, index) {
        if (widget.showAddButton && index == 0) {
          return _buildAddAudioItem();
        }

        final audioIndex = widget.showAddButton ? index - 1 : index;
        final audioItem = widget.audioItems[audioIndex];
        return _buildAudioItem(audioItem);
      },
    );
  }

  Widget _buildAddAudioItem() {
    return GestureDetector(
      onTap: widget.onAddAudioTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: const Color(0xFF2D2C2F),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0xFF565656),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomPaint(
              painter: DashedBorderPainter(
                color: const Color(0xFF8A8D93),
                strokeWidth: 1,
                dashLength: 5,
                gapLength: 5,
                radius: 16,
              ),
              child: const SizedBox(
                width: 56,
                height: 56,
                child: Icon(
                  Icons.add,
                  color: Color(0xff8A8D93),
                  size: 30,
                ),
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              "录制音频",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAudioItem(AudioItem audioItem) {
    final isSelected = _selectedItemIds.contains(audioItem.id);
    final isCurrentSelected = _currentSelectedItemId == audioItem.id;

    return GestureDetector(
      onTap: () {
        if (_isManagementMode) {
          _toggleItemSelection(audioItem.id);
        } else {
          _selectAudioItem(audioItem);
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: const Color(0xFF2D2C2F),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isCurrentSelected
                ? const Color(0xFF30E6B8) // 选中时的青绿色边框
                : const Color(0xFF565656), // 默认边框颜色
            width: isCurrentSelected ? 2 : 1, // 选中时边框更粗
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: const Color(0xFF4B4B4B),
                    borderRadius: BorderRadius.circular(56),
                  ),
                  child: audioItem.cover != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(56),
                          child: Image.network(
                            audioItem.cover!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(
                                Icons.music_note,
                                color: Colors.white,
                                size: 24,
                              );
                            },
                          ),
                        )
                      : const Icon(
                          Icons.music_note,
                          color: Colors.white,
                          size: 24,
                        ),
                ),
                const SizedBox(height: 12),
                Text(
                  audioItem.title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    color: Colors.white, // 默认文字颜色
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            ),
            // 选中状态指示器
            // if (isCurrentSelected && !_isManagementMode)
            //   Positioned(
            //     top: 4,
            //     right: 4,
            //     child: Container(
            //       width: 16,
            //       height: 16,
            //       decoration: const BoxDecoration(
            //         color: Color(0xFF30E6B8),
            //         shape: BoxShape.circle,
            //       ),
            //       child: const Icon(
            //         Icons.check,
            //         color: Colors.white,
            //         size: 10,
            //       ),
            //     ),
            //   ),
            if (_isManagementMode)
              Positioned(
                top: 0,
                right: 0,
                child: AnimatedBuilder(
                  animation: _managementModeAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _managementModeAnimation.value,
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: isSelected
                              ? const Color(0xFF30E6B8)
                              : Colors.transparent,
                          border: Border.all(
                            color: isSelected
                                ? const Color(0xFF30E6B8)
                                : const Color(0xFF8A8D93),
                            width: 2,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                color: Color(0xff2D2C2F),
                                size: 14,
                              )
                            : null,
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeleteButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _showDeleteConfirmationDialog,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFF4757),
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          "删除选中项 (${_selectedItemIds.length})",
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

/// Delete confirmation dialog that slides up from the bottom
class _DeleteConfirmationDialog extends StatelessWidget {
  const _DeleteConfirmationDialog({
    required this.selectedItems,
    required this.onConfirm,
    required this.onCancel,
  });

  final List<AudioItem> selectedItems;
  final VoidCallback onConfirm;
  final VoidCallback onCancel;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF222123),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Padding(
            padding: EdgeInsets.fromLTRB(0, 24, 0, 8),
            child: Column(
              children: [
                Text(
                  "确认要删除吗",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 120,
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: const BoxDecoration(
              color: Color(0xFF2D2C2F),
              borderRadius: BorderRadius.all(
                Radius.circular(16),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                GestureDetector(
                  onTap: onConfirm,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    width: double.infinity,
                    child: const Text(
                      "确认",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                Container(
                  height: 1,
                  color: const Color(0xFF565656),
                ),
                GestureDetector(
                  onTap: onCancel,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    width: double.infinity,
                    child: const Text(
                      "取消",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Color(0xFFF4565F),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
