import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/audio/selected_audio_provider.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';

/// 显示当前选中音频和图片状态的组件
class SelectedAudioStatusWidget extends ConsumerWidget {
  const SelectedAudioStatusWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedAudio = ref.watch(selectedAudioProvider);
    final photoModification = ref.watch(photoModificationCurrentProvider);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2C2F),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF565656),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          const Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Color(0xFF30E6B8),
                size: 18,
              ),
              SizedBox(width: 8),
              Text(
                '生成状态',
                style: TextStyle(
                  color: Color(0xFF30E6B8),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // 音频状态
          _buildStatusItem(
            icon: Icons.music_note,
            title: '音频',
            status: selectedAudio != null ? '已选择' : '未选择',
            detail: selectedAudio?.title,
            isReady: selectedAudio != null,
          ),
          
          const SizedBox(height: 8),
          
          // 图片状态
          _buildStatusItem(
            icon: Icons.image,
            title: '图片',
            status: (photoModification.remoteUrl?.isNotEmpty ?? false) ? '已上传' : '未上传',
            detail: (photoModification.remoteUrl?.isNotEmpty ?? false) ? '图片已准备就绪' : null,
            isReady: photoModification.remoteUrl?.isNotEmpty ?? false,
          ),
          
          const SizedBox(height: 12),
          
          // 整体状态
          _buildOverallStatus(
            audioReady: selectedAudio != null,
            imageReady: photoModification.remoteUrl?.isNotEmpty ?? false,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem({
    required IconData icon,
    required String title,
    required String status,
    String? detail,
    required bool isReady,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: isReady ? const Color(0xFF30E6B8) : const Color(0xFF8A8D93),
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          '$title: ',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
          ),
        ),
        Text(
          status,
          style: TextStyle(
            color: isReady ? const Color(0xFF30E6B8) : const Color(0xFFFF4757),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        if (detail != null) ...[
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              '($detail)',
              style: const TextStyle(
                color: Color(0xFF8A8D93),
                fontSize: 12,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildOverallStatus({
    required bool audioReady,
    required bool imageReady,
  }) {
    final isReady = audioReady && imageReady;
    final statusText = isReady 
        ? '✓ 已准备就绪，可以生成视频'
        : '⚠ 请完成音频和图片的选择/上传';
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: isReady 
            ? const Color(0xFF30E6B8).withOpacity(0.1)
            : const Color(0xFFFF4757).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isReady ? const Color(0xFF30E6B8) : const Color(0xFFFF4757),
          width: 1,
        ),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: isReady ? const Color(0xFF30E6B8) : const Color(0xFFFF4757),
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
