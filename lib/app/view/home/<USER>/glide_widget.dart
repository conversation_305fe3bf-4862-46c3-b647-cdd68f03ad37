import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:ui_widgets/ui_widgets.dart';

class GlideWidget extends ConsumerStatefulWidget {
  const GlideWidget({super.key});

  @override
  GlideWidgetState createState() => GlideWidgetState();
}

class GlideWidgetState extends ConsumerState<GlideWidget> {
  late StreamSubscription<List<ConnectivityResult>> subscription;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (d) {
        subscription = Connectivity().onConnectivityChanged.listen(
          (List<ConnectivityResult> result) {
            debugPrint("onConnectivityChanged: ${result.length}");
            debugPrint("onConnectivityChanged: ${result.first}");
            final hasNetwork = result.any((r) => r != ConnectivityResult.none);
            if (hasNetwork) {
              debugPrint("onConnectivityChanged: refresh");
              // ref.read(publicDigitalFilterListProvider.notifier).getFilter();
              // ref.read(publicDigitalHumanListProvider.notifier).loadData();
            }
          },
        );
      },
    );
  }

  @override
  void dispose() {
    subscription.cancel();
    super.dispose();
  }

  // creation header
  Widget _buildHeadBanner() {
    var itemWidth = 170.w;
    var itemHeight = 94 * itemWidth / 170;
    return SliverToBoxAdapter(
      child: Container(
        height: itemHeight + 29,
        padding: const EdgeInsets.only(top: 9, bottom: 20),
        child: ListView.separated(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          scrollDirection: Axis.horizontal,
          itemCount: 5,
          itemBuilder: (BuildContext context, int index) {
            return InkWell(
              onTap: () {
                if (index == 0) {
                  context.push("/$photoModificationPage");
                }
              },
              child: Container(
                width: itemWidth,
                height: itemHeight,
                color: Colors.grey,
              ),
            ) ;
          },
          separatorBuilder: (BuildContext context, int index) {
            return const SizedBox(width: 6);
          },
        ),
      ),
    );
  }

  Widget _buildHeadNavi() {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.only(top: 4, bottom: 20),
        child: Row(
          children: [
            const SizedBox(width: 8),
            Expanded(
              child: Row(
                children: [1, 2, 3, 4]
                    .map(
                      (e) => Expanded(
                        child: InkWell(
                          onTap: () {
                            if (e == 1) {
                              context.push("/$textToVideoPage");
                            } else if (e == 2) {
                              context.push("/$oldPhotoRestorationPage");
                            } else if (e == 3) {
                              context.push("/$qualityRestorationPage");
                            } else if (e == 4) {
                              context.push("/$photoMattingPage");
                            }
                          },
                          child: Column(
                            children: [
                              Container(
                                width: 20,
                                height: 20,
                                color: Colors.grey,
                              ),
                              const SizedBox(height: 7),
                              const Text(
                                "AI 故事视频",
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
            const SizedBox(width: 8),
            InkWell(
              onTap: () {},
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Column(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 7),
                    const Text(
                      "全部",
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
        ),
      ),
    );
  }

  Widget _buildItem(String title) {
    var itemWidth = 97.5.w;
    var itemHeight = 130 * itemWidth / 97.5;
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                  ),
                ),
                const Row(
                  children: [
                    Text(
                      "全部",
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF94979D),
                      ),
                    ),
                    SizedBox(width: 2),
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: Color(0xFF94979D),
                      size: 9,
                    ),
                  ],
                )
              ],
            ),
          ),
          const SizedBox(height: 10),
          SizedBox(
            height: itemHeight,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemBuilder: (context, index) {
                return InkWell(
                  onTap: () {
                    if (index == 0) {
                      context.push("/$petSingPage");// 宠物唱歌
                    }
                    if (index == 1) {
                      context.push("/$syncSingPage");// 对口型唱歌
                    }
                    if (index == 2) {
                      context.push("/$cartoonDigimonPage");// 卡通化
                    }
                  },
                  child: ColoredBox(
                    color: Colors.grey,
                    child: SizedBox(
                      width: itemWidth,
                      height: itemHeight,
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return const SizedBox(width: 10);
              },
              itemCount: 10,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CustomListView(
      padding: EdgeInsets.only(
        bottom: MediaQuery.paddingOf(context).bottom + 71,
      ),
      sliverHeader: [
        SliverToBoxAdapter(
          child: SizedBox(
            height: MediaQuery.paddingOf(context).top,
          ),
        ),
        _buildHeadBanner(),
        _buildHeadNavi(),
      ],
      onLoadMore: () async {},
      data: const [1, 2, 3, 4, 5, 6, 7, 8],
      renderItem: (context, index, o) {
        return _buildItem("AI 视频");
      },
    );
  }
}
