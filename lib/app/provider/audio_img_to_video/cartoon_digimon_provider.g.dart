// // GENERATED CODE - DO NOT MODIFY BY HAND

// part of 'cartoon_digimon_provider.dart';

// // **************************************************************************
// // RiverpodGenerator
// // **************************************************************************

// String _$cartoonDigimonVideoHash() =>
//     r'6febbbe0ca18c55d0f7d676736b03ab1d7ee2517';

// /// 卡通数字人视频生成状态管理Provider
// ///
// /// Copied from [CartoonDigimonVideo].
// @ProviderFor(CartoonDigimonVideo)
// final cartoonDigimonVideoProvider =
//     AutoDisposeNotifierProvider<CartoonDigimonVideo, AudioItem?>.internal(
//   CartoonDigimonVideo.new,
//   name: r'cartoonDigimonVideoProvider',
//   debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
//       ? null
//       : _$cartoonDigimonVideoHash,
//   dependencies: null,
//   allTransitiveDependencies: null,
// );

// typedef _$CartoonDigimonVideo = AutoDisposeNotifier<AudioItem?>;
// // ignore_for_file: type=lint
// // ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
