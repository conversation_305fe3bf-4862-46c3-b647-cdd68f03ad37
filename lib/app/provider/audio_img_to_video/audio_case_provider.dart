import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/repository/modals/audio_img_to_video/audio_img_to_video.dart';
import 'package:text_generation_video/app/repository/service/audio_img_to_video_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';

part 'audio_case_provider.g.dart';

/// 获取对口型案例列表
/// caseType
/// 1:卡通数字人
/// 2:对口型唱歌
/// 3:宠物唱歌
@riverpod
Future<List<AudioImgToVideo>?> fetchLipSyncCase(Ref ref, PageType caseType) async {
  var result = await AudioImgToVideoService.listLipSyncCase(caseType);
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

enum PageType {
  cartoonDigimon,
  syncSinging,
  petSinging,
}

@riverpod
class AudioCaseToVideoCase extends _$AudioCaseToVideoCase {
  @override
  AudioImgToVideo? build() {
    return null;
  }

  void selectCase(AudioImgToVideo? caseItem) {
    state = caseItem;
  }

  // 提交音频模版和图片
  void commit(PageType commitType) async {
    final currentUploadImages = ref.read(photoModificationCurrentProvider);
    final remoteUrl = currentUploadImages.remoteUrl;
    final audioUrl = state?.audioUrl;
    if (remoteUrl == null || remoteUrl.isEmpty) {
      ToastUtil.showToast("请先上传图片");
      return;
    }
    if (audioUrl == null || audioUrl.isEmpty) {
      ToastUtil.showToast("请选择歌曲模版");
      return;
    }
    SmartDialog.showLoading(msg: "提交中...");
    ApiResponse<bool> result;
    if (commitType == PageType.syncSinging) {
      result = await AudioImgToVideoService.lipSyncSinging(
          audioUrl: audioUrl, imageUrl: remoteUrl);
    } else {
      result = await AudioImgToVideoService.petSinging(
          audioUrl: audioUrl, imageUrl: remoteUrl);
    }
    SmartDialog.dismiss();

    if (result.status == Status.completed) {
      ToastUtil.showToast("提交成功");
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}
