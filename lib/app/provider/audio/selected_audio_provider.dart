import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/repository/modals/audio_img_to_video/audio_item.dart';
import 'package:text_generation_video/utils/toast_util.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';

import 'package:text_generation_video/app/repository/service/audio_img_to_video_service.dart';

part 'selected_audio_provider.g.dart';

/// 当前选中音频的状态管理Provider
/// 用于管理用户在音频列表中选中的音频项
@riverpod
class SelectedAudio extends _$SelectedAudio {
  @override
  AudioItem? build() {
    return null;
  }

  /// 选择音频项
  void selectAudio(AudioItem? audioItem) {
    state = audioItem;
    if (audioItem != null) {
      debugPrint('SelectedAudio Provider: 选中音频 - ${audioItem.title}');
    } else {
      debugPrint('SelectedAudio Provider: 取消选中音频');
    }
  }

  /// 清除选中的音频
  void clearSelection() {
    state = null;
    debugPrint('SelectedAudio Provider: 清除音频选择');
  }

  /// 提交音频和图片生成卡通数字人视频
  Future<void> generateVideo() async {
    final currentUploadImages = ref.read(photoModificationCurrentProvider);
    final remoteUrl = currentUploadImages.remoteUrl;
    debugPrint("state: $state");
    final audioUrl = state?.audioUrl;
    if (remoteUrl == null || remoteUrl.isEmpty) {
      ToastUtil.showToast("请先上传图片");
      return;
    }
    if (audioUrl == null || audioUrl.isEmpty) {
      ToastUtil.showToast("请选择音频");
      return;
    }
    SmartDialog.showLoading(msg: "提交中...");
    ApiResponse<bool> result;

    result = await AudioImgToVideoService.cartoonDigimon(
        audioUrl: audioUrl, imageUrl: remoteUrl);

    SmartDialog.dismiss();

    if (result.status == Status.completed) {
      ToastUtil.showToast("提交成功");
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}
