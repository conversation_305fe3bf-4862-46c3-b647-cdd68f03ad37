// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'selected_audio_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selectedAudioHash() => r'0a4be1c0573bd8ee8ca69009f9fff294ee8e900d';

/// 当前选中音频的状态管理Provider
/// 用于管理用户在音频列表中选中的音频项
///
/// Copied from [SelectedAudio].
@ProviderFor(SelectedAudio)
final selectedAudioProvider =
    NotifierProvider<SelectedAudio, AudioItem?>.internal(
  SelectedAudio.new,
  name: r'selectedAudioProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedAudioHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedAudio = Notifier<AudioItem?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
